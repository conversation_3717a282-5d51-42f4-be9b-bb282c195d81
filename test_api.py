import asyncio
from mexc_client import MEXCClient

async def test_api():
    async with MEXCClient() as client:
        data = await client.get_price_data()
        if data:
            print('✅ API MEXC hoạt động tốt')
            print(f'PAXG Price: ${data["current_price"]}')
            print(f'24h Change: {data["price_change_percent"]}%')
        else:
            print('❌ Không thể kết nối API MEXC')

if __name__ == "__main__":
    asyncio.run(test_api())
