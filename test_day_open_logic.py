#!/usr/bin/env python3

import asyncio
from mexc_client import MEXCClient
from discord_bot import PAXGBot
from datetime import datetime

async def test_day_open_logic():
    print("🧪 Test Day Open Price Logic")
    print("=" * 60)
    
    async with MEXCClient() as client:
        # Test raw kline data
        print("📊 Testing Kline Data Retrieval:")
        kline_data = await client.get_klines(interval="1d", limit=1)
        
        if kline_data and len(kline_data) > 0:
            kline = kline_data[0]
            print(f"   Raw kline data: {kline}")
            print(f"   Open time: {datetime.fromtimestamp(kline[0]/1000)}")
            print(f"   Day open price: ${float(kline[1]):,.2f}")
            print(f"   Day high: ${float(kline[2]):,.2f}")
            print(f"   Day low: ${float(kline[3]):,.2f}")
            print(f"   Current close: ${float(kline[4]):,.2f}")
            print(f"   Volume: {float(kline[5]):,.2f}")
        else:
            print("   ❌ No kline data received")
            return
        
        # Test processed price data
        print(f"\n📊 Testing Processed Price Data:")
        price_data = await client.get_price_data()
        
        if price_data:
            print(f"   Current price: ${price_data['current_price']:,.2f}")
            print(f"   Day open price: ${price_data['day_open_price']:,.2f}")
            print(f"   Day change: ${price_data['day_price_change']:,.2f}")
            print(f"   Day change %: {price_data['day_price_change_percent']:.2f}%")
            print(f"   24h open price: ${price_data['open_price_24h']:,.2f}")
            print(f"   24h change: ${price_data['price_change_24h']:,.2f}")
            print(f"   24h change %: {price_data['price_change_percent_24h']:.2f}%")
            
            # Manual verification
            manual_day_change = price_data['current_price'] - price_data['day_open_price']
            manual_day_percent = (manual_day_change / price_data['day_open_price'] * 100) if price_data['day_open_price'] > 0 else 0
            
            print(f"\n🔍 Manual Verification:")
            print(f"   Manual day change: ${manual_day_change:,.2f}")
            print(f"   Manual day %: {manual_day_percent:.2f}%")
            print(f"   Matches calculated: {abs(manual_day_percent - price_data['day_price_change_percent']) < 0.01}")
            
            # Test embed creation
            print(f"\n📋 Testing New Embed Format:")
            bot = PAXGBot()
            embed = bot.create_price_embed(price_data, is_initial=True)
            
            print(f"   Title: {embed.title}")
            print(f"   Color: {embed.color}")
            print(f"   Fields: {len(embed.fields)}")
            
            for i, field in enumerate(embed.fields, 1):
                print(f"   {i}. {field.name}: {field.value}")
            
            # Test với giá giảm (simulation)
            print(f"\n🧪 Testing Negative Change Simulation:")
            negative_data = price_data.copy()
            negative_data['day_price_change'] = -15.50
            negative_data['day_price_change_percent'] = -0.46
            negative_data['price_change_24h'] = -8.20
            negative_data['price_change_percent_24h'] = -0.25
            
            negative_embed = bot.create_price_embed(negative_data)
            day_change_field = next((f for f in negative_embed.fields if "Thay đổi trong ngày" in f.name), None)
            h24_change_field = next((f for f in negative_embed.fields if "Thay đổi 24h" in f.name), None)
            
            if day_change_field and h24_change_field:
                print(f"   Day change field: {day_change_field.name}: {day_change_field.value}")
                print(f"   24h change field: {h24_change_field.name}: {h24_change_field.value}")
                
                day_has_red = "🔴" in day_change_field.name
                h24_has_red = "🔴" in h24_change_field.name
                print(f"   Day red icon: {'✅' if day_has_red else '❌'}")
                print(f"   24h red icon: {'✅' if h24_has_red else '❌'}")
            
            print(f"\n🎯 Summary of Changes:")
            print(f"   ✅ Added day open price from kline data")
            print(f"   ✅ Added day change calculation")
            print(f"   ✅ Added day change percentage")
            print(f"   ✅ Kept 24h change for comparison")
            print(f"   ✅ Updated embed to show both changes")
            print(f"   ✅ Color based on day change (not 24h)")
            print(f"   ✅ Icons for both day and 24h changes")
            
            # Example output
            print(f"\n📱 Example Output Format:")
            day_icon = "🟢" if price_data['day_price_change'] >= 0 else "🔴"
            h24_icon = "🟢" if price_data['price_change_24h'] >= 0 else "🔴"
            
            print(f"   📈 CẬP NHẬT GIÁ PAXG")
            print(f"   💰 Giá hiện tại: ${price_data['current_price']:,.2f}")
            print(f"   🌅 Giá mở cửa ngày: ${price_data['day_open_price']:,.2f}")
            print(f"   {day_icon} Thay đổi trong ngày: ${abs(price_data['day_price_change']):,.2f} ({abs(price_data['day_price_change_percent']):.2f}%)")
            print(f"   {h24_icon} Thay đổi 24h: ${abs(price_data['price_change_24h']):,.2f} ({abs(price_data['price_change_percent_24h']):.2f}%)")
            print(f"   📊 Cao nhất 24h: ${price_data['high_price']:,.2f}")
            print(f"   📊 Thấp nhất 24h: ${price_data['low_price']:,.2f}")
            print(f"   📦 Volume 24h: {price_data['volume']:,.2f} PAXG")
            
        else:
            print("   ❌ Could not get processed price data")

if __name__ == "__main__":
    asyncio.run(test_day_open_logic())
