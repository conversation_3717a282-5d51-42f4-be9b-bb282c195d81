#!/usr/bin/env python3

import asyncio
from discord_bot import PAXGBot
from mexc_client import MEXCClient

async def test_watchlist_format():
    print("🧪 Test Watchlist Format")
    print("-" * 50)
    
    bot = PAXGBot()
    
    async with MEXCClient() as client:
        price_data = await client.get_price_data()
        
        if price_data:
            embed = bot.create_price_embed(price_data, is_initial=True)
            
            print(f"Title: {embed.title}")
            print(f"Color: {embed.color}")
            print(f"Description:")
            print(embed.description)
            print(f"\nFooter: {embed.footer.text}")
            
            print(f"\n📱 Mobile Preview:")
            print("=" * 30)
            print(f"{embed.title}")
            print("-" * 30)
            print(embed.description)
            print("-" * 30)
            print(f"{embed.footer.text}")
            print("=" * 30)
            
        else:
            print("❌ Could not get price data")

if __name__ == "__main__":
    asyncio.run(test_watchlist_format())
