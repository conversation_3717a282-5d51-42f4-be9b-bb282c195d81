#!/usr/bin/env python3

import asyncio
import sys
import time
from mexc_client import MEXCClient
from discord_bot import PAXGBot

async def test_mexc_api():
    print("🧪 Test 1: MEXC API Connection")
    print("-" * 50)
    
    try:
        async with MEXCClient() as client:
            data = await client.get_price_data()
            if data:
                print("✅ API kết nối thành công")
                print(f"   Symbol: {data['symbol']}")
                print(f"   Current Price: ${data['current_price']:,.4f}")
                print(f"   24h Change: {data['price_change_percent']:+.4f}%")
                print(f"   Volume: {data['volume']:,.2f}")
                print(f"   High: ${data['high_price']:,.4f}")
                print(f"   Low: ${data['low_price']:,.4f}")
                return True
            else:
                print("❌ Không thể lấy dữ liệu từ API")
                return False
    except Exception as e:
        print(f"❌ Lỗi API: {e}")
        return False

async def test_price_monitoring():
    print("\n🧪 Test 2: Price Monitoring Logic")
    print("-" * 50)
    
    try:
        async with MEXCClient() as client:
            # Lấy dữ liệu lần 1
            data1 = await client.get_price_data()
            if not data1:
                print("❌ Không thể lấy dữ liệu lần 1")
                return False
            
            print(f"   Lần 1 - Price: ${data1['current_price']:,.4f}, Volume: {data1['volume']:,.2f}")
            
            # Đợi 5 giây
            await asyncio.sleep(5)
            
            # Lấy dữ liệu lần 2
            data2 = await client.get_price_data()
            if not data2:
                print("❌ Không thể lấy dữ liệu lần 2")
                return False
                
            print(f"   Lần 2 - Price: ${data2['current_price']:,.4f}, Volume: {data2['volume']:,.2f}")
            
            # Tính toán thay đổi
            price_change = abs((data2['current_price'] - data1['current_price']) / data1['current_price'] * 100) if data1['current_price'] > 0 else 0
            volume_change = abs((data2['volume'] - data1['volume']) / data1['volume'] * 100) if data1['volume'] > 0 else 0
            
            print(f"   Price Change: {price_change:.4f}%")
            print(f"   Volume Change: {volume_change:.4f}%")
            
            # Test logic cảnh báo
            price_threshold = 0.5
            volume_threshold = 20
            
            if price_change >= price_threshold:
                print(f"🚨 Sẽ có cảnh báo giá (>{price_threshold}%)")
            else:
                print(f"✅ Không cảnh báo giá (<{price_threshold}%)")
                
            if volume_change >= volume_threshold:
                print(f"🚨 Sẽ có cảnh báo volume (>{volume_threshold}%)")
            else:
                print(f"✅ Không cảnh báo volume (<{volume_threshold}%)")
                
            return True
            
    except Exception as e:
        print(f"❌ Lỗi monitoring: {e}")
        return False

def test_embed_creation():
    print("\n🧪 Test 3: Discord Embed Creation")
    print("-" * 50)
    
    try:
        # Tạo dữ liệu mẫu
        sample_data = {
            'symbol': 'PAXGUSDT',
            'current_price': 3348.92,
            'open_price': 3347.50,
            'high_price': 3355.00,
            'low_price': 3340.00,
            'volume': 1234.56,
            'quote_volume': 4123456.78,
            'price_change': 1.42,
            'price_change_percent': 0.0424,
            'weighted_avg_price': 3349.25,
            'prev_close_price': 3347.50,
            'bid_price': 3348.90,
            'ask_price': 3349.00
        }
        
        # Test tạo embed thông thường
        bot = PAXGBot()
        embed1 = bot.create_price_embed(sample_data, is_initial=True)
        print("✅ Tạo embed khởi tạo thành công")
        print(f"   Title: {embed1.title}")
        print(f"   Fields: {len(embed1.fields)}")
        
        # Test tạo embed cảnh báo giá
        embed2 = bot.create_price_embed(sample_data, alert_type="price")
        print("✅ Tạo embed cảnh báo giá thành công")
        print(f"   Title: {embed2.title}")
        
        # Test tạo embed cảnh báo volume
        embed3 = bot.create_price_embed(sample_data, alert_type="volume")
        print("✅ Tạo embed cảnh báo volume thành công")
        print(f"   Title: {embed3.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi tạo embed: {e}")
        return False

def test_config_loading():
    print("\n🧪 Test 4: Config Loading")
    print("-" * 50)
    
    try:
        bot = PAXGBot()
        config = bot.config
        
        print("✅ Load config thành công")
        print(f"   Guild ID: {config['discord']['guild_id']}")
        print(f"   Admin ID: {config['discord']['admin_id']}")
        print(f"   Command Prefix: {config['discord']['command_prefix']}")
        print(f"   Token: {'*' * 20}...{config['discord']['token'][-10:]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi load config: {e}")
        return False

async def test_threshold_logic():
    print("\n🧪 Test 5: Threshold Logic")
    print("-" * 50)
    
    try:
        bot = PAXGBot()
        
        # Test threshold mặc định
        print(f"✅ Price threshold mặc định: {bot.price_threshold}%")
        print(f"✅ Volume threshold mặc định: {bot.volume_threshold}%")
        
        # Test thay đổi threshold
        bot.price_threshold = 1.0
        bot.volume_threshold = 30.0
        
        print(f"✅ Price threshold sau khi thay đổi: {bot.price_threshold}%")
        print(f"✅ Volume threshold sau khi thay đổi: {bot.volume_threshold}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi threshold logic: {e}")
        return False

async def main():
    print("🚀 BẮT ĐẦU TEST TẤT CẢ TÍNH NĂNG BOT PAXG")
    print("=" * 60)
    
    results = []
    
    # Test 1: MEXC API
    results.append(await test_mexc_api())
    
    # Test 2: Price Monitoring
    results.append(await test_price_monitoring())
    
    # Test 3: Embed Creation
    results.append(test_embed_creation())
    
    # Test 4: Config Loading
    results.append(test_config_loading())
    
    # Test 5: Threshold Logic
    results.append(await test_threshold_logic())
    
    # Tổng kết
    print("\n" + "=" * 60)
    print("📊 KẾT QUẢ TEST")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "MEXC API Connection",
        "Price Monitoring Logic", 
        "Discord Embed Creation",
        "Config Loading",
        "Threshold Logic"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n🎯 Tổng kết: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 TẤT CẢ TÍNH NĂNG HOẠT ĐỘNG TỐTTT!")
    else:
        print("⚠️  Một số tính năng cần kiểm tra lại")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
