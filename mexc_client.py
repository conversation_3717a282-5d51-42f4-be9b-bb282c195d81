import aiohttp
import asyncio
import json
import logging
from typing import Dict, Optional

class MEXCClient:
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_ticker_24hr(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting ticker: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_ticker_24hr: {e}")
            return None
    
    async def get_klines(self, symbol: str = "PAXGUSDT", interval: str = "1d", limit: int = 1) -> Optional[list]:
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting klines: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_klines: {e}")
            return None
    
    async def get_price_data(self) -> Optional[Dict]:
        ticker_data = await self.get_ticker_24hr()
        kline_data = await self.get_klines()

        if not ticker_data:
            return None

        def safe_float(value, default=0.0):
            try:
                return float(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        def safe_int(value, default=0):
            try:
                return int(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        result = {
            "symbol": ticker_data.get("symbol", "PAXGUSDT"),
            "current_price": safe_float(ticker_data.get("lastPrice")),
            "open_price": safe_float(ticker_data.get("openPrice")),
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume")),
            "quote_volume": safe_float(ticker_data.get("quoteVolume")),
            "price_change": safe_float(ticker_data.get("priceChange")),
            "price_change_percent": safe_float(ticker_data.get("priceChangePercent")),
            "weighted_avg_price": safe_float(ticker_data.get("weightedAvgPrice")),
            "prev_close_price": safe_float(ticker_data.get("prevClosePrice")),
            "bid_price": safe_float(ticker_data.get("bidPrice")),
            "ask_price": safe_float(ticker_data.get("askPrice")),
            "count": safe_int(ticker_data.get("count"))
        }

        return result
