# PAXG Discord Bot

Bot Discord theo dõi giá PAXG future tại MEXC Exchange với các tính năng tự động cập nhật và cảnh báo.

## Tính năng

- 📊 Hiển thị thông tin giá PAXG real-time
- 📌 Pin tin nhắn với thông tin giá khi khởi động
- 🚨 Cảnh báo khi giá dao động >= 0.5%
- 📦 Cảnh báo khi volume dao động >= 20%
- ⚙️ Cấu hình ngưỡng cảnh báo tùy chỉnh
- 🔄 Cập nhật tự động mỗi 30 giây
- 💹 Hiển thị đầy đủ thông tin thị trường

## Yêu cầu hệ thống

- Python 3.8+
- Discord Bot Token
- Kết nối internet ổn định

## Cài đặt

### 1. Cài đặt dependencies:
```bash
pip3 install -r requirements.txt
```

### 2. <PERSON><PERSON><PERSON> hình Discord:
- Tạo channel tên `paxg` trong Discord server
- <PERSON><PERSON><PERSON> bảo bot có quyền:
  - Send Messages
  - Manage Messages (để pin tin nhắn)
  - Embed Links
  - Read Message History

### 3. Chạy bot:
```bash
python3 main.py
```

### 4. Kiểm tra hoạt động:
Bot sẽ tự động:
- Gửi tin nhắn thông tin giá PAXG và pin tin nhắn đó
- Cập nhật tin nhắn đã pin mỗi 30 giây
- Gửi cảnh báo khi có dao động lớn

## Lệnh

- `!paxg` - Kiểm tra giá PAXG thủ công
- `!threshold <price_threshold> <volume_threshold>` - Cài đặt ngưỡng cảnh báo (chỉ admin)

## Cấu hình

File `config.ymal` chứa:
- Discord bot token
- Guild ID
- Admin ID
- Command prefix

## Thông tin hiển thị

- 💰 Giá hiện tại
- 🌅 Giá mở cửa ngày
- 📈/📉 Thay đổi 24h
- 📊 Cao/thấp nhất 24h
- 📦 Volume 24h
- 💵 Quote volume
- ⚖️ Giá trung bình
- 💹 Bid/Ask

## Cảnh báo

Bot sẽ gửi cảnh báo khi:
- Giá thay đổi >= 0.5% (mặc định)
- Volume thay đổi >= 20% (mặc định)

Cập nhật mỗi 30 giây.
