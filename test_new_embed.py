#!/usr/bin/env python3

import asyncio
from discord_bot import PAXGBot
from mexc_client import MEXCClient

async def test_new_embed_format():
    print("🧪 Test New Embed Format")
    print("-" * 50)
    
    bot = PAXGBot()
    
    async with MEXCClient() as client:
        price_data = await client.get_price_data()
        
        if price_data:
            print("📊 Current PAXG Data:")
            print(f"   Current Price: ${price_data['current_price']:,.2f}")
            print(f"   Open Price: ${price_data['open_price']:,.2f}")
            print(f"   Price Change: ${price_data['price_change']:,.2f}")
            print(f"   Price Change %: {price_data['price_change_percent']:.2f}%")
            print(f"   High: ${price_data['high_price']:,.2f}")
            print(f"   Low: ${price_data['low_price']:,.2f}")
            print(f"   Volume: {price_data['volume']:,.2f} PAXG")
            
            # Test embed creation
            embed = bot.create_price_embed(price_data, is_initial=True)
            
            print(f"\n📋 Embed Fields:")
            print(f"   Title: {embed.title}")
            print(f"   Color: {embed.color}")
            print(f"   Fields count: {len(embed.fields)}")
            
            for i, field in enumerate(embed.fields):
                print(f"   {i+1}. {field.name}: {field.value}")
            
            print(f"   Footer: {embed.footer.text}")
            
            # Test alert embed
            alert_embed = bot.create_price_embed(price_data, alert_type="price")
            print(f"\n🚨 Alert Embed Title: {alert_embed.title}")
            
            print("\n✅ New format features:")
            print("   ✅ Removed Quote Volume field")
            print("   ✅ Removed Weighted Average field") 
            print("   ✅ Removed Bid/Ask field")
            print("   ✅ Price format: 2 decimal places")
            print("   ✅ Change format: 🟢/🔴 icons instead of +/-")
            print("   ✅ Correct percentage calculation")
            print("   ✅ Only 6 fields instead of 9")
            
        else:
            print("❌ Could not get price data")

if __name__ == "__main__":
    asyncio.run(test_new_embed_format())
