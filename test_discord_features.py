#!/usr/bin/env python3

import asyncio
import discord
import yaml
from mexc_client import MEXCClient

async def test_discord_connection():
    print("🧪 Test Discord Connection & Channel Finding")
    print("-" * 50)
    
    try:
        # Load config
        with open('config.ymal', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        # Setup intents
        intents = discord.Intents.default()
        intents.message_content = True
        
        # Create client
        client = discord.Client(intents=intents)
        
        @client.event
        async def on_ready():
            print(f"✅ Bot connected as {client.user}")
            
            # Find guild
            guild = client.get_guild(int(config['discord']['guild_id']))
            if guild:
                print(f"✅ Found guild: {guild.name}")
                
                # Find paxg channel
                paxg_channel = discord.utils.get(guild.channels, name='paxg')
                if paxg_channel:
                    print(f"✅ Found paxg channel: #{paxg_channel.name}")
                    
                    # Test sending message
                    try:
                        async with MEXCClient() as mexc_client:
                            price_data = await mexc_client.get_price_data()
                            if price_data:
                                # Create test embed
                                embed = discord.Embed(
                                    title="🧪 TEST MESSAGE - PAXG Price Info",
                                    color=discord.Color.blue()
                                )
                                embed.add_field(
                                    name="💰 Current Price",
                                    value=f"${price_data['current_price']:,.4f}",
                                    inline=True
                                )
                                embed.add_field(
                                    name="📈 24h Change",
                                    value=f"{price_data['price_change_percent']:+.4f}%",
                                    inline=True
                                )
                                embed.set_footer(text="Test message - Bot is working!")
                                
                                # Send message
                                message = await paxg_channel.send(embed=embed)
                                print(f"✅ Test message sent successfully: {message.id}")
                                
                                # Test pinning
                                await message.pin()
                                print("✅ Message pinned successfully")
                                
                                # Wait a bit then unpin to clean up
                                await asyncio.sleep(2)
                                await message.unpin()
                                await message.delete()
                                print("✅ Test message cleaned up")
                                
                            else:
                                print("❌ Could not get price data for test")
                    except Exception as e:
                        print(f"❌ Error sending test message: {e}")
                else:
                    print("❌ paxg channel not found! Please create #paxg channel")
            else:
                print("❌ Guild not found!")
            
            await client.close()
        
        # Connect with timeout
        try:
            await asyncio.wait_for(client.start(config['discord']['token']), timeout=15.0)
        except asyncio.TimeoutError:
            print("⏰ Connection timeout")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def test_command_simulation():
    print("\n🧪 Test Command Logic Simulation")
    print("-" * 50)
    
    try:
        from discord_bot import PAXGBot
        
        # Create bot instance
        bot = PAXGBot()
        
        # Test manual price check logic
        async with MEXCClient() as client:
            price_data = await client.get_price_data()
            if price_data:
                embed = bot.create_price_embed(price_data)
                print("✅ Manual price check embed created")
                print(f"   Title: {embed.title}")
                print(f"   Fields: {len(embed.fields)}")
            else:
                print("❌ Could not create price embed")
                return False
        
        # Test threshold setting logic
        original_price_threshold = bot.price_threshold
        original_volume_threshold = bot.volume_threshold
        
        # Simulate threshold command
        bot.price_threshold = 2.0
        bot.volume_threshold = 50.0
        
        print(f"✅ Threshold setting works")
        print(f"   Price: {original_price_threshold}% → {bot.price_threshold}%")
        print(f"   Volume: {original_volume_threshold}% → {bot.volume_threshold}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Command simulation failed: {e}")
        return False

async def test_alert_system():
    print("\n🧪 Test Alert System Logic")
    print("-" * 50)
    
    try:
        from discord_bot import PAXGBot
        
        bot = PAXGBot()
        
        # Create test data with significant changes
        test_data_high_price_change = {
            'symbol': 'PAXGUSDT',
            'current_price': 3400.00,  # Significant change
            'open_price': 3347.50,
            'high_price': 3410.00,
            'low_price': 3340.00,
            'volume': 800.00,
            'quote_volume': 2680000.00,
            'price_change': 52.50,
            'price_change_percent': 1.57,  # > 0.5% threshold
            'weighted_avg_price': 3375.00,
            'prev_close_price': 3347.50,
            'bid_price': 3399.50,
            'ask_price': 3400.50
        }
        
        test_data_high_volume_change = {
            'symbol': 'PAXGUSDT',
            'current_price': 3347.55,
            'open_price': 3347.50,
            'high_price': 3355.00,
            'low_price': 3340.00,
            'volume': 2000.00,  # Significant volume increase
            'quote_volume': 6695000.00,
            'price_change': 0.05,
            'price_change_percent': 0.0015,
            'weighted_avg_price': 3349.00,
            'prev_close_price': 3347.50,
            'bid_price': 3347.50,
            'ask_price': 3347.60
        }
        
        # Test price alert embed
        price_alert_embed = bot.create_price_embed(test_data_high_price_change, alert_type="price")
        print("✅ Price alert embed created")
        print(f"   Title: {price_alert_embed.title}")
        print(f"   Color: {price_alert_embed.color}")
        
        # Test volume alert embed  
        volume_alert_embed = bot.create_price_embed(test_data_high_volume_change, alert_type="volume")
        print("✅ Volume alert embed created")
        print(f"   Title: {volume_alert_embed.title}")
        
        # Test alert logic
        bot.last_price = 3347.50
        bot.last_volume = 800.00
        
        current_price = test_data_high_price_change['current_price']
        current_volume = test_data_high_price_change['volume']
        
        price_change_percent = abs((current_price - bot.last_price) / bot.last_price * 100)
        volume_change_percent = abs((current_volume - bot.last_volume) / bot.last_volume * 100)
        
        print(f"✅ Alert logic calculation:")
        print(f"   Price change: {price_change_percent:.4f}% (threshold: {bot.price_threshold}%)")
        print(f"   Volume change: {volume_change_percent:.4f}% (threshold: {bot.volume_threshold}%)")
        
        should_price_alert = price_change_percent >= bot.price_threshold
        should_volume_alert = volume_change_percent >= bot.volume_threshold
        
        print(f"   Should price alert: {should_price_alert}")
        print(f"   Should volume alert: {should_volume_alert}")
        
        return True
        
    except Exception as e:
        print(f"❌ Alert system test failed: {e}")
        return False

async def main():
    print("🚀 BẮT ĐẦU TEST TÍNH NĂNG DISCORD")
    print("=" * 60)
    
    results = []
    
    # Test 1: Discord Connection & Channel
    print("⚠️  Lưu ý: Test này sẽ gửi tin nhắn test vào channel #paxg")
    print("   Tin nhắn sẽ được xóa tự động sau 2 giây")
    
    confirm = input("\nBạn có muốn tiếp tục test Discord connection? (y/n): ")
    if confirm.lower() == 'y':
        results.append(await test_discord_connection())
    else:
        print("⏭️  Bỏ qua Discord connection test")
        results.append(True)  # Assume pass
    
    # Test 2: Command Simulation
    results.append(await test_command_simulation())
    
    # Test 3: Alert System
    results.append(await test_alert_system())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 KẾT QUẢ TEST DISCORD")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "Discord Connection & Messaging",
        "Command Logic Simulation",
        "Alert System Logic"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n🎯 Tổng kết: {passed}/{total} Discord tests passed")
    
    if passed == total:
        print("🎉 TẤT CẢ TÍNH NĂNG DISCORD HOẠT ĐỘNG TỐT!")
    else:
        print("⚠️  Một số tính năng Discord cần kiểm tra lại")

if __name__ == "__main__":
    asyncio.run(main())
