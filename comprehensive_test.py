#!/usr/bin/env python3

import asyncio
import sys
import time
from mexc_client import MEXCClient
from discord_bot import PAXGBot
import discord
import yaml

async def test_all_features():
    print("🚀 COMPREHENSIVE TEST - TẤT CẢ TÍNH NĂNG BOT PAXG")
    print("=" * 70)
    
    test_results = {}
    
    # Test 1: MEXC API Connection
    print("\n🧪 Test 1: MEXC API Connection & Data Retrieval")
    print("-" * 50)
    try:
        async with MEXCClient() as client:
            data = await client.get_price_data()
            if data and data['current_price'] > 0:
                print("✅ MEXC API connection successful")
                print(f"   Current PAXG Price: ${data['current_price']:,.4f}")
                print(f"   24h Change: {data['price_change_percent']:+.4f}%")
                print(f"   Volume: {data['volume']:,.2f} PAXG")
                test_results['mexc_api'] = True
            else:
                print("❌ MEXC API failed to return valid data")
                test_results['mexc_api'] = False
    except Exception as e:
        print(f"❌ MEXC API error: {e}")
        test_results['mexc_api'] = False
    
    # Test 2: Bot Configuration
    print("\n🧪 Test 2: Bot Configuration Loading")
    print("-" * 50)
    try:
        bot = PAXGBot()
        config = bot.config
        
        required_keys = ['discord']
        discord_keys = ['token', 'guild_id', 'admin_id', 'command_prefix']
        
        config_valid = True
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing config key: {key}")
                config_valid = False
        
        if 'discord' in config:
            for key in discord_keys:
                if key not in config['discord']:
                    print(f"❌ Missing discord config key: {key}")
                    config_valid = False
        
        if config_valid:
            print("✅ Configuration loaded successfully")
            print(f"   Guild ID: {config['discord']['guild_id']}")
            print(f"   Admin ID: {config['discord']['admin_id']}")
            print(f"   Command Prefix: {config['discord']['command_prefix']}")
            test_results['config'] = True
        else:
            test_results['config'] = False
            
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        test_results['config'] = False
    
    # Test 3: Discord Embed Creation
    print("\n🧪 Test 3: Discord Embed Creation")
    print("-" * 50)
    try:
        bot = PAXGBot()
        
        # Sample data for testing
        sample_data = {
            'symbol': 'PAXGUSDT',
            'current_price': 3348.92,
            'open_price': 3347.50,
            'high_price': 3355.00,
            'low_price': 3340.00,
            'volume': 1234.56,
            'quote_volume': 4123456.78,
            'price_change': 1.42,
            'price_change_percent': 0.0424,
            'weighted_avg_price': 3349.25,
            'prev_close_price': 3347.50,
            'bid_price': 3348.90,
            'ask_price': 3349.00
        }
        
        # Test different embed types
        embed_tests = [
            ('initial', bot.create_price_embed(sample_data, is_initial=True)),
            ('update', bot.create_price_embed(sample_data)),
            ('price_alert', bot.create_price_embed(sample_data, alert_type="price")),
            ('volume_alert', bot.create_price_embed(sample_data, alert_type="volume"))
        ]
        
        embed_success = True
        for embed_type, embed in embed_tests:
            if embed and embed.title and len(embed.fields) > 0:
                print(f"✅ {embed_type.capitalize()} embed created successfully")
                print(f"   Title: {embed.title}")
                print(f"   Fields: {len(embed.fields)}")
            else:
                print(f"❌ {embed_type.capitalize()} embed creation failed")
                embed_success = False
        
        test_results['embeds'] = embed_success
        
    except Exception as e:
        print(f"❌ Embed creation error: {e}")
        test_results['embeds'] = False
    
    # Test 4: Alert Logic
    print("\n🧪 Test 4: Alert System Logic")
    print("-" * 50)
    try:
        bot = PAXGBot()
        
        # Test threshold defaults
        print(f"✅ Default thresholds set:")
        print(f"   Price threshold: {bot.price_threshold}%")
        print(f"   Volume threshold: {bot.volume_threshold}%")
        
        # Test threshold modification
        original_price = bot.price_threshold
        original_volume = bot.volume_threshold
        
        bot.price_threshold = 1.0
        bot.volume_threshold = 30.0
        
        print(f"✅ Threshold modification works:")
        print(f"   Price: {original_price}% → {bot.price_threshold}%")
        print(f"   Volume: {original_volume}% → {bot.volume_threshold}%")
        
        # Test alert calculation logic
        bot.last_price = 3300.00
        bot.last_volume = 1000.00
        
        current_price = 3350.00  # ~1.5% increase
        current_volume = 1500.00  # 50% increase
        
        price_change = abs((current_price - bot.last_price) / bot.last_price * 100)
        volume_change = abs((current_volume - bot.last_volume) / bot.last_volume * 100)
        
        should_price_alert = price_change >= bot.price_threshold
        should_volume_alert = volume_change >= bot.volume_threshold
        
        print(f"✅ Alert calculation logic:")
        print(f"   Price change: {price_change:.2f}% → Alert: {should_price_alert}")
        print(f"   Volume change: {volume_change:.2f}% → Alert: {should_volume_alert}")
        
        test_results['alerts'] = True
        
    except Exception as e:
        print(f"❌ Alert system error: {e}")
        test_results['alerts'] = False
    
    # Test 5: Price Monitoring Simulation
    print("\n🧪 Test 5: Price Monitoring Simulation")
    print("-" * 50)
    try:
        async with MEXCClient() as client:
            # Get initial data
            data1 = await client.get_price_data()
            if not data1:
                raise Exception("Could not get initial price data")
            
            print(f"   Initial: ${data1['current_price']:,.4f}, Vol: {data1['volume']:,.2f}")
            
            # Wait and get second data point
            await asyncio.sleep(3)
            data2 = await client.get_price_data()
            if not data2:
                raise Exception("Could not get second price data")
            
            print(f"   After 3s: ${data2['current_price']:,.4f}, Vol: {data2['volume']:,.2f}")
            
            # Calculate changes
            price_change = abs((data2['current_price'] - data1['current_price']) / data1['current_price'] * 100) if data1['current_price'] > 0 else 0
            volume_change = abs((data2['volume'] - data1['volume']) / data1['volume'] * 100) if data1['volume'] > 0 else 0
            
            print(f"   Changes: Price {price_change:.4f}%, Volume {volume_change:.4f}%")
            
            # Simulate monitoring logic
            bot = PAXGBot()
            bot.last_price = data1['current_price']
            bot.last_volume = data1['volume']
            
            would_alert_price = price_change >= bot.price_threshold
            would_alert_volume = volume_change >= bot.volume_threshold
            
            print(f"   Would trigger alerts: Price={would_alert_price}, Volume={would_alert_volume}")
            
            test_results['monitoring'] = True
            
    except Exception as e:
        print(f"❌ Price monitoring simulation error: {e}")
        test_results['monitoring'] = False
    
    # Test 6: Bot Initialization (without Discord connection)
    print("\n🧪 Test 6: Bot Initialization")
    print("-" * 50)
    try:
        bot = PAXGBot()
        
        # Check if bot has required attributes
        required_attrs = ['config', 'paxg_channel', 'pinned_message', 'last_price', 'last_volume', 'price_threshold', 'volume_threshold']
        
        init_success = True
        for attr in required_attrs:
            if not hasattr(bot, attr):
                print(f"❌ Missing bot attribute: {attr}")
                init_success = False
        
        if init_success:
            print("✅ Bot initialization successful")
            print(f"   All required attributes present")
            print(f"   Bot user: {bot.user}")
            print(f"   Command prefix: {bot.command_prefix}")
        
        test_results['initialization'] = init_success
        
    except Exception as e:
        print(f"❌ Bot initialization error: {e}")
        test_results['initialization'] = False
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    test_names = {
        'mexc_api': 'MEXC API Connection',
        'config': 'Configuration Loading',
        'embeds': 'Discord Embed Creation',
        'alerts': 'Alert System Logic',
        'monitoring': 'Price Monitoring Simulation',
        'initialization': 'Bot Initialization'
    }
    
    passed = 0
    total = len(test_results)
    
    for key, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{passed + 1}. {test_names[key]}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 TỔNG KẾT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 TẤT CẢ TÍNH NĂNG HOẠT ĐỘNG HOÀN HẢO!")
        print("✅ Bot sẵn sàng để sử dụng trong Discord server")
    elif passed >= total * 0.8:
        print("⚠️  Hầu hết tính năng hoạt động tốt, một số cần kiểm tra")
    else:
        print("❌ Nhiều tính năng cần được sửa chữa")
    
    # Recommendations
    print("\n📋 KHUYẾN NGHỊ:")
    if not test_results.get('mexc_api', True):
        print("- Kiểm tra kết nối internet và MEXC API")
    if not test_results.get('config', True):
        print("- Kiểm tra file config.ymal")
    if passed == total:
        print("- Tạo channel #paxg trong Discord server")
        print("- Chạy: python3 main.py")
        print("- Bot sẽ tự động gửi và pin thông tin giá PAXG")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(test_all_features())
    sys.exit(0 if success else 1)
