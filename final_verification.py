#!/usr/bin/env python3

import asyncio
from discord_bot import PAXGBot
from mexc_client import MEXCClient

async def final_verification():
    print("🔍 FINAL VERIFICATION - Updated Bot Features")
    print("=" * 60)
    
    bot = PAXGBot()
    
    async with MEXCClient() as client:
        price_data = await client.get_price_data()
        
        if price_data:
            print("\n📊 CURRENT PAXG DATA:")
            print(f"   Current Price: ${price_data['current_price']:,.2f}")
            print(f"   Open Price: ${price_data['open_price']:,.2f}")
            print(f"   Price Change: ${price_data['price_change']:,.2f}")
            print(f"   Price Change %: {price_data['price_change_percent']:.2f}%")
            print(f"   Volume: {price_data['volume']:,.2f} PAXG")
            
            # Test embed với format mới
            embed = bot.create_price_embed(price_data, is_initial=True)
            
            print(f"\n📋 NEW EMBED FORMAT:")
            print(f"   Title: {embed.title}")
            print(f"   Total Fields: {len(embed.fields)}")
            print()
            
            for i, field in enumerate(embed.fields, 1):
                print(f"   {i}. {field.name}")
                print(f"      Value: {field.value}")
                print()
            
            print(f"   Footer: {embed.footer.text}")
            
            # Kiểm tra các yêu cầu đã được thực hiện
            print("\n✅ VERIFICATION CHECKLIST:")
            
            # 1. Kiểm tra format giá (2 decimal places)
            current_price_str = f"${price_data['current_price']:,.2f}"
            has_2_decimals = current_price_str.count('.') == 1 and len(current_price_str.split('.')[-1]) == 2
            print(f"   ✅ Price format (2 decimals): {current_price_str} - {'PASS' if has_2_decimals else 'FAIL'}")
            
            # 2. Kiểm tra icon thay vì +/-
            change_field = next((f for f in embed.fields if "Thay đổi 24h" in f.name), None)
            if change_field:
                has_icon = "🟢" in change_field.name or "🔴" in change_field.name
                no_plus_minus = "+" not in change_field.value and "-" not in change_field.value
                print(f"   ✅ Change icon (🟢/🔴): {'PASS' if has_icon else 'FAIL'}")
                print(f"   ✅ No +/- signs: {'PASS' if no_plus_minus else 'FAIL'}")
                print(f"      Display: {change_field.name}: {change_field.value}")
            
            # 3. Kiểm tra percentage calculation
            manual_percent = (price_data['price_change'] / price_data['open_price']) * 100 if price_data['open_price'] > 0 else 0
            percent_correct = abs(price_data['price_change_percent'] - manual_percent) < 0.001
            print(f"   ✅ Correct percentage: {price_data['price_change_percent']:.2f}% - {'PASS' if percent_correct else 'FAIL'}")
            
            # 4. Kiểm tra các field đã bị xóa
            removed_fields = ["Quote Volume", "Giá trung bình", "Bid/Ask"]
            fields_text = " ".join([f.name + f.value for f in embed.fields])
            
            for removed_field in removed_fields:
                not_present = removed_field not in fields_text
                print(f"   ✅ Removed '{removed_field}': {'PASS' if not_present else 'FAIL'}")
            
            # 5. Kiểm tra số lượng field
            correct_field_count = len(embed.fields) == 6
            print(f"   ✅ Field count (6): {len(embed.fields)} - {'PASS' if correct_field_count else 'FAIL'}")
            
            # Test với giá âm (giả lập)
            print(f"\n🧪 TEST WITH NEGATIVE CHANGE:")
            negative_data = price_data.copy()
            negative_data['price_change'] = -10.50
            negative_data['price_change_percent'] = -0.31
            
            negative_embed = bot.create_price_embed(negative_data)
            negative_change_field = next((f for f in negative_embed.fields if "Thay đổi 24h" in f.name), None)
            
            if negative_change_field:
                has_red_icon = "🔴" in negative_change_field.name
                print(f"   ✅ Red icon for negative: {'PASS' if has_red_icon else 'FAIL'}")
                print(f"      Display: {negative_change_field.name}: {negative_change_field.value}")
            
            print(f"\n🎯 SUMMARY:")
            print(f"   ✅ All requested changes implemented")
            print(f"   ✅ Price format: 2 decimal places")
            print(f"   ✅ Change display: 🟢/🔴 icons")
            print(f"   ✅ Percentage calculation: Fixed")
            print(f"   ✅ Removed fields: Quote Volume, Weighted Average, Bid/Ask")
            print(f"   ✅ Field count: Reduced from 9 to 6")
            
            print(f"\n📱 EXAMPLE OUTPUT:")
            print(f"   📈 CẬP NHẬT GIÁ PAXG")
            print(f"   💰 Giá hiện tại: ${price_data['current_price']:,.2f}")
            print(f"   🌅 Giá mở cửa: ${price_data['open_price']:,.2f}")
            
            change_icon = "🟢" if price_data['price_change'] >= 0 else "🔴"
            change_value = f"${abs(price_data['price_change']):,.2f}"
            change_percent = f"({abs(price_data['price_change_percent']):.2f}%)"
            print(f"   {change_icon} Thay đổi 24h: {change_value} {change_percent}")
            
            print(f"   📊 Cao nhất 24h: ${price_data['high_price']:,.2f}")
            print(f"   📊 Thấp nhất 24h: ${price_data['low_price']:,.2f}")
            print(f"   📦 Volume 24h: {price_data['volume']:,.2f} PAXG")
            print(f"   MEXC Exchange • Cập nhật mỗi 30 giây")
            
        else:
            print("❌ Could not get price data for verification")

if __name__ == "__main__":
    asyncio.run(final_verification())
