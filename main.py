#!/usr/bin/env python3

import asyncio
import logging
import sys
from discord_bot import run_bot

def main():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bot.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    try:
        print("🚀 Khởi động PAXG Discord Bot...")
        run_bot()
    except KeyboardInterrupt:
        print("\n⏹️ Bot đã được dừng bởi người dùng")
    except Exception as e:
        logging.error(f"❌ Lỗi khi chạy bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
