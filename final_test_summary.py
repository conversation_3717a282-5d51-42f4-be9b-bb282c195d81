#!/usr/bin/env python3

import asyncio
from mexc_client import MEXCClient
from discord_bot import PAXGBot
import yaml

def print_header(title):
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_section(title):
    print(f"\n📋 {title}")
    print("-" * 40)

async def final_comprehensive_test():
    print_header("FINAL COMPREHENSIVE TEST - BOT PAXG DISCORD")
    
    all_tests_passed = True
    
    # Test 1: Core API Functionality
    print_section("1. MEXC API Core Functionality")
    try:
        async with MEXCClient() as client:
            # Test basic connection
            ticker = await client.get_ticker_24hr()
            klines = await client.get_klines()
            price_data = await client.get_price_data()
            
            if ticker and price_data:
                print("✅ MEXC API fully functional")
                print(f"   ├─ Ticker data: ✅")
                print(f"   ├─ Klines data: {'✅' if klines else '⚠️'}")
                print(f"   ├─ Price data: ✅")
                print(f"   ├─ Current price: ${price_data['current_price']:,.4f}")
                print(f"   ├─ 24h change: {price_data['price_change_percent']:+.4f}%")
                print(f"   └─ Volume: {price_data['volume']:,.2f} PAXG")
            else:
                print("❌ MEXC API failed")
                all_tests_passed = False
    except Exception as e:
        print(f"❌ MEXC API error: {e}")
        all_tests_passed = False
    
    # Test 2: Bot Configuration & Setup
    print_section("2. Bot Configuration & Setup")
    try:
        with open('config.ymal', 'r') as f:
            config = yaml.safe_load(f)
        
        bot = PAXGBot()
        
        print("✅ Bot configuration loaded")
        print(f"   ├─ Discord token: {'✅' if config['discord']['token'] else '❌'}")
        print(f"   ├─ Guild ID: {config['discord']['guild_id']}")
        print(f"   ├─ Admin ID: {config['discord']['admin_id']}")
        print(f"   ├─ Command prefix: {config['discord']['command_prefix']}")
        print(f"   ├─ Price threshold: {bot.price_threshold}%")
        print(f"   └─ Volume threshold: {bot.volume_threshold}%")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        all_tests_passed = False
    
    # Test 3: Discord Embed System
    print_section("3. Discord Embed System")
    try:
        bot = PAXGBot()
        async with MEXCClient() as client:
            real_data = await client.get_price_data()
            
            if real_data:
                # Test all embed types
                embeds = {
                    'Initial': bot.create_price_embed(real_data, is_initial=True),
                    'Update': bot.create_price_embed(real_data),
                    'Price Alert': bot.create_price_embed(real_data, alert_type="price"),
                    'Volume Alert': bot.create_price_embed(real_data, alert_type="volume")
                }
                
                print("✅ Discord embed system functional")
                for embed_type, embed in embeds.items():
                    fields_count = len(embed.fields) if embed else 0
                    status = "✅" if embed and fields_count >= 9 else "❌"
                    print(f"   ├─ {embed_type}: {status} ({fields_count} fields)")
                
                print(f"   └─ All embeds contain real PAXG data")
            else:
                print("❌ Could not create embeds with real data")
                all_tests_passed = False
                
    except Exception as e:
        print(f"❌ Embed system error: {e}")
        all_tests_passed = False
    
    # Test 4: Alert Logic System
    print_section("4. Alert Logic System")
    try:
        bot = PAXGBot()
        
        # Test with realistic scenarios
        scenarios = [
            {"name": "Small change", "old_price": 3347.50, "new_price": 3348.00, "old_vol": 800, "new_vol": 810},
            {"name": "Price spike", "old_price": 3347.50, "new_price": 3365.00, "old_vol": 800, "new_vol": 820},
            {"name": "Volume surge", "old_price": 3347.50, "new_price": 3348.00, "old_vol": 800, "new_vol": 1200},
            {"name": "Both alerts", "old_price": 3347.50, "new_price": 3370.00, "old_vol": 800, "new_vol": 1300}
        ]
        
        print("✅ Alert logic system functional")
        for scenario in scenarios:
            price_change = abs((scenario["new_price"] - scenario["old_price"]) / scenario["old_price"] * 100)
            volume_change = abs((scenario["new_vol"] - scenario["old_vol"]) / scenario["old_vol"] * 100)
            
            price_alert = price_change >= bot.price_threshold
            volume_alert = volume_change >= bot.volume_threshold
            
            alerts = []
            if price_alert: alerts.append("PRICE")
            if volume_alert: alerts.append("VOLUME")
            alert_str = ", ".join(alerts) if alerts else "NONE"
            
            print(f"   ├─ {scenario['name']}: P:{price_change:.2f}% V:{volume_change:.1f}% → {alert_str}")
        
    except Exception as e:
        print(f"❌ Alert logic error: {e}")
        all_tests_passed = False
    
    # Test 5: Real-time Monitoring Capability
    print_section("5. Real-time Monitoring Capability")
    try:
        print("✅ Testing real-time data fetching...")
        
        async with MEXCClient() as client:
            # Test multiple data points
            data_points = []
            for i in range(3):
                data = await client.get_price_data()
                if data:
                    data_points.append(data)
                    print(f"   ├─ Data point {i+1}: ${data['current_price']:,.4f} (Vol: {data['volume']:,.1f})")
                await asyncio.sleep(2)
            
            if len(data_points) >= 2:
                # Calculate stability
                prices = [d['current_price'] for d in data_points]
                volumes = [d['volume'] for d in data_points]
                
                price_stability = max(prices) - min(prices)
                volume_stability = max(volumes) - min(volumes)
                
                print(f"   ├─ Price stability: ${price_stability:.4f} range")
                print(f"   ├─ Volume stability: {volume_stability:.1f} range")
                print(f"   └─ Monitoring system: ✅ READY")
            else:
                print("   └─ ⚠️  Limited data points collected")
                
    except Exception as e:
        print(f"❌ Monitoring capability error: {e}")
        all_tests_passed = False
    
    # Test 6: Bot Runtime Readiness
    print_section("6. Bot Runtime Readiness")
    try:
        bot = PAXGBot()
        
        # Check all required components
        components = {
            'Config loaded': hasattr(bot, 'config') and bot.config is not None,
            'Thresholds set': hasattr(bot, 'price_threshold') and hasattr(bot, 'volume_threshold'),
            'Last values initialized': hasattr(bot, 'last_price') and hasattr(bot, 'last_volume'),
            'Channel reference ready': hasattr(bot, 'paxg_channel'),
            'Pinned message ready': hasattr(bot, 'pinned_message'),
            'Commands registered': len(bot.commands) > 0
        }
        
        print("✅ Bot runtime readiness check")
        for component, status in components.items():
            status_icon = "✅" if status else "❌"
            print(f"   ├─ {component}: {status_icon}")
        
        ready_count = sum(components.values())
        total_count = len(components)
        print(f"   └─ Readiness: {ready_count}/{total_count} components ready")
        
        if ready_count < total_count:
            all_tests_passed = False
            
    except Exception as e:
        print(f"❌ Runtime readiness error: {e}")
        all_tests_passed = False
    
    # Final Summary
    print_header("FINAL TEST SUMMARY")
    
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - BOT IS FULLY FUNCTIONAL!")
        print("\n📋 DEPLOYMENT CHECKLIST:")
        print("   ✅ MEXC API integration working")
        print("   ✅ Discord bot configuration ready")
        print("   ✅ Embed system functional")
        print("   ✅ Alert logic operational")
        print("   ✅ Real-time monitoring capable")
        print("   ✅ Bot runtime components ready")
        
        print("\n🚀 NEXT STEPS:")
        print("   1. Create #paxg channel in your Discord server")
        print("   2. Run: python3 main.py")
        print("   3. Bot will automatically:")
        print("      • Connect to Discord")
        print("      • Send initial PAXG price info")
        print("      • Pin the message")
        print("      • Update every 30 seconds")
        print("      • Send alerts on price/volume changes")
        
        print("\n💡 FEATURES READY:")
        print("   • Real-time PAXG price tracking")
        print("   • Automatic message pinning")
        print("   • Price change alerts (≥0.5%)")
        print("   • Volume change alerts (≥20%)")
        print("   • Manual commands (!paxg, !threshold)")
        print("   • Admin controls")
        
    else:
        print("⚠️  SOME TESTS FAILED - REVIEW REQUIRED")
        print("\n📋 TROUBLESHOOTING:")
        print("   • Check internet connection")
        print("   • Verify config.ymal file")
        print("   • Ensure Discord bot token is valid")
        print("   • Create #paxg channel in Discord server")
    
    return all_tests_passed

if __name__ == "__main__":
    success = asyncio.run(final_comprehensive_test())
    print(f"\n{'🎯 SUCCESS' if success else '❌ FAILED'}: Final test {'completed' if success else 'failed'}")
