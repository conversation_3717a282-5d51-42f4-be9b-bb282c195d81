import discord
from discord.ext import commands, tasks
import asyncio
import yaml
import logging
from datetime import datetime
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO)

class PAXGBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = False
        super().__init__(command_prefix='!', intents=intents)
        
        self.config = self.load_config()
        self.paxg_channel = None
        self.pinned_message = None
        self.last_price = 0
        self.last_volume = 0
        self.price_threshold = 0.5  # 0.5% thay đổi giá
        self.volume_threshold = 20  # 20% thay đổi volume
        
    def load_config(self):
        with open('config.ymal', 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    
    async def on_ready(self):
        print(f'{self.user} đã kết nối thành công!')
        
        guild = self.get_guild(int(self.config['discord']['guild_id']))
        if guild:
            self.paxg_channel = discord.utils.get(guild.channels, name='paxg')
            if not self.paxg_channel:
                print("Không tìm thấy channel 'paxg'. Vui lòng tạo channel này.")
                return
        
        self.price_monitor.start()
        await self.send_initial_price_info()
    
    async def send_initial_price_info(self):
        async with MEXCClient() as client:
            price_data = await client.get_price_data()
            if price_data:
                embed = self.create_price_embed(price_data, is_initial=True)
                message = await self.paxg_channel.send(embed=embed)
                await message.pin()
                self.pinned_message = message
                self.last_price = price_data['current_price']
                self.last_volume = price_data['volume']
    
    def create_price_embed(self, data, is_initial=False, alert_type=None):
        current_price = data['current_price']
        open_price = data['open_price']
        price_change = data['price_change']
        price_change_percent = data['price_change_percent']
        
        color = discord.Color.green() if price_change >= 0 else discord.Color.red()
        
        if alert_type:
            if alert_type == "price":
                title = "🚨 CẢNH BÁO GIÁ PAXG"
            elif alert_type == "volume":
                title = "🚨 CẢNH BÁO VOLUME PAXG"
            else:
                title = "🚨 CẢNH BÁO PAXG"
        elif is_initial:
            title = "📊 THÔNG TIN GIÁ PAXG FUTURE - MEXC"
        else:
            title = "📈 CẬP NHẬT GIÁ PAXG"
        
        embed = discord.Embed(title=title, color=color, timestamp=datetime.now())
        
        embed.add_field(
            name="💰 Giá hiện tại",
            value=f"${current_price:,.4f}",
            inline=True
        )
        
        embed.add_field(
            name="🌅 Giá mở cửa",
            value=f"${open_price:,.4f}",
            inline=True
        )
        
        change_emoji = "📈" if price_change >= 0 else "📉"
        embed.add_field(
            name=f"{change_emoji} Thay đổi 24h",
            value=f"${price_change:+,.4f} ({price_change_percent:+.2f}%)",
            inline=True
        )
        
        embed.add_field(
            name="📊 Cao nhất 24h",
            value=f"${data['high_price']:,.4f}",
            inline=True
        )
        
        embed.add_field(
            name="📊 Thấp nhất 24h",
            value=f"${data['low_price']:,.4f}",
            inline=True
        )
        
        embed.add_field(
            name="📦 Volume 24h",
            value=f"{data['volume']:,.2f} PAXG",
            inline=True
        )
        
        embed.add_field(
            name="💵 Quote Volume",
            value=f"${data['quote_volume']:,.2f}",
            inline=True
        )
        
        embed.add_field(
            name="⚖️ Giá trung bình",
            value=f"${data['weighted_avg_price']:,.4f}",
            inline=True
        )
        
        embed.add_field(
            name="💹 Bid/Ask",
            value=f"${data['bid_price']:,.4f} / ${data['ask_price']:,.4f}",
            inline=True
        )
        
        embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")
        
        return embed
    
    @tasks.loop(seconds=30)
    async def price_monitor(self):
        try:
            async with MEXCClient() as client:
                price_data = await client.get_price_data()
                if not price_data:
                    return
                
                current_price = price_data['current_price']
                current_volume = price_data['volume']
                
                price_change_percent = abs((current_price - self.last_price) / self.last_price * 100) if self.last_price > 0 else 0
                volume_change_percent = abs((current_volume - self.last_volume) / self.last_volume * 100) if self.last_volume > 0 else 0
                
                should_alert = False
                alert_type = None
                
                if price_change_percent >= self.price_threshold:
                    should_alert = True
                    alert_type = "price"
                elif volume_change_percent >= self.volume_threshold:
                    should_alert = True
                    alert_type = "volume"
                
                if should_alert:
                    embed = self.create_price_embed(price_data, alert_type=alert_type)
                    await self.paxg_channel.send(embed=embed)
                
                if self.pinned_message:
                    embed = self.create_price_embed(price_data)
                    await self.pinned_message.edit(embed=embed)
                
                self.last_price = current_price
                self.last_volume = current_volume
                
        except Exception as e:
            logging.error(f"Error in price_monitor: {e}")
    
    @commands.command(name='paxg')
    async def manual_price_check(self, ctx):
        async with MEXCClient() as client:
            price_data = await client.get_price_data()
            if price_data:
                embed = self.create_price_embed(price_data)
                await ctx.send(embed=embed)
            else:
                await ctx.send("❌ Không thể lấy dữ liệu giá PAXG")
    
    @commands.command(name='threshold')
    async def set_threshold(self, ctx, price_threshold: float = None, volume_threshold: float = None):
        if ctx.author.id != int(self.config['discord']['admin_id']):
            await ctx.send("❌ Bạn không có quyền sử dụng lệnh này")
            return
        
        if price_threshold is not None:
            self.price_threshold = price_threshold
        if volume_threshold is not None:
            self.volume_threshold = volume_threshold
        
        await ctx.send(f"✅ Đã cập nhật ngưỡng cảnh báo:\n"
                      f"📊 Giá: {self.price_threshold}%\n"
                      f"📦 Volume: {self.volume_threshold}%")

def run_bot():
    bot = PAXGBot()
    bot.run(bot.config['discord']['token'])
