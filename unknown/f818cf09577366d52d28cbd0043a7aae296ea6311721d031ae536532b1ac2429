import aiohttp
import asyncio
import json
import logging
from typing import Dict, Optional

class MEXCClient:
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_ticker_24hr(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting ticker: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_ticker_24hr: {e}")
            return None
    
    async def get_klines(self, symbol: str = "PAXGUSDT", interval: str = "1d", limit: int = 1) -> Optional[list]:
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting klines: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_klines: {e}")
            return None
    
    async def get_price_data(self) -> Optional[Dict]:
        ticker_data = await self.get_ticker_24hr()
        kline_data = await self.get_klines(interval="1d", limit=1)

        if not ticker_data:
            return None

        def safe_float(value, default=0.0):
            try:
                return float(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        def safe_int(value, default=0):
            try:
                return int(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))

        # Lấy giá mở cửa từ nến ngày hiện tại
        day_open_price = current_price  # fallback
        if kline_data and len(kline_data) > 0:
            # Kline format: [open_time, open, high, low, close, volume, close_time, quote_volume, count, taker_buy_volume, taker_buy_quote_volume, ignore]
            day_open_price = safe_float(kline_data[0][1])  # index 1 là open price

        # Tính toán thay đổi so với giá mở cửa ngày
        day_price_change = current_price - day_open_price
        day_price_change_percent = (day_price_change / day_open_price * 100) if day_open_price > 0 else 0.0

        # Thông tin 24h từ ticker
        price_change_24h = safe_float(ticker_data.get("priceChange"))
        open_price_24h = safe_float(ticker_data.get("openPrice"))
        price_change_percent_24h = (price_change_24h / open_price_24h * 100) if open_price_24h > 0 else 0.0

        result = {
            "symbol": ticker_data.get("symbol", "PAXGUSDT"),
            "current_price": current_price,
            "day_open_price": day_open_price,  # Giá mở cửa nến ngày
            "day_price_change": day_price_change,  # Thay đổi so với mở cửa ngày
            "day_price_change_percent": day_price_change_percent,  # % thay đổi so với mở cửa ngày
            "open_price_24h": open_price_24h,  # Giá mở cửa 24h
            "price_change_24h": price_change_24h,  # Thay đổi 24h
            "price_change_percent_24h": price_change_percent_24h,  # % thay đổi 24h
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume")),
            "quote_volume": safe_float(ticker_data.get("quoteVolume"))
        }

        return result
