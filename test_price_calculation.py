#!/usr/bin/env python3

import asyncio
from mexc_client import MEXCClient

async def test_price_calculation():
    print("🧪 Test Price Change Calculation")
    print("-" * 50)
    
    async with MEXCClient() as client:
        # <PERSON><PERSON>y dữ liệu raw từ API
        ticker_data = await client.get_ticker_24hr()
        price_data = await client.get_price_data()
        
        if ticker_data and price_data:
            print("📊 Raw API Data:")
            print(f"   lastPrice: {ticker_data.get('lastPrice')}")
            print(f"   openPrice: {ticker_data.get('openPrice')}")
            print(f"   priceChange: {ticker_data.get('priceChange')}")
            print(f"   priceChangePercent: {ticker_data.get('priceChangePercent')}")
            
            print("\n📊 Processed Data:")
            print(f"   current_price: {price_data['current_price']}")
            print(f"   open_price: {price_data['open_price']}")
            print(f"   price_change: {price_data['price_change']}")
            print(f"   price_change_percent: {price_data['price_change_percent']}")
            
            # Tính toán thủ công để kiểm tra
            if price_data['open_price'] > 0:
                manual_change = price_data['current_price'] - price_data['open_price']
                manual_percent = (manual_change / price_data['open_price']) * 100
                
                print("\n🔍 Manual Calculation:")
                print(f"   Manual change: {manual_change:.4f}")
                print(f"   Manual percent: {manual_percent:.4f}%")
                
                print("\n📈 Comparison:")
                print(f"   API percent: {price_data['price_change_percent']:.4f}%")
                print(f"   Manual percent: {manual_percent:.4f}%")
                print(f"   Difference: {abs(price_data['price_change_percent'] - manual_percent):.4f}%")
                
                # Test format hiển thị
                change_icon = "🟢" if price_data['price_change'] >= 0 else "🔴"
                change_value = f"${abs(price_data['price_change']):,.2f}"
                change_percent = f"({abs(price_data['price_change_percent']):.2f}%)"
                
                print(f"\n💡 Display Format:")
                print(f"   {change_icon} Thay đổi 24h: {change_value} {change_percent}")
                
                # Test với dữ liệu mẫu từ user
                print(f"\n🧪 Test với dữ liệu user:")
                user_current = 3348.60
                user_open = 3343.20
                user_change = 5.40
                user_percent_raw = (user_change / user_open) * 100
                
                print(f"   Current: ${user_current}")
                print(f"   Open: ${user_open}")
                print(f"   Change: ${user_change}")
                print(f"   Calculated %: {user_percent_raw:.4f}%")
                print(f"   User showed: 0.00% (WRONG)")
                
                user_icon = "🟢" if user_change >= 0 else "🔴"
                user_display = f"{user_icon} Thay đổi 24h: ${abs(user_change):,.2f} ({abs(user_percent_raw):.2f}%)"
                print(f"   Correct display: {user_display}")
                
        else:
            print("❌ Không thể lấy dữ liệu từ API")

if __name__ == "__main__":
    asyncio.run(test_price_calculation())
